{"version": 3, "file": "WebSocketServer.js", "sourceRoot": "", "sources": ["../../../src/bidiServer/WebSocketServer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;GAeG;AACH,gDAAwB;AAGxB,kDAA0B;AAC1B,qDAAuC;AAIvC,8CAAwC;AAExC,6DAAqD;AAExC,QAAA,SAAS,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AACnD,MAAM,aAAa,GAAG,IAAA,eAAK,EAAC,sBAAsB,CAAC,CAAC;AACpD,MAAM,SAAS,GAAG,IAAA,eAAK,EAAC,oBAAoB,CAAC,CAAC;AAC9C,MAAM,SAAS,GAAG,IAAA,eAAK,EAAC,oBAAoB,CAAC,CAAC;AAyB9C,MAAa,eAAe;IAC1B,SAAS,GAAG,IAAI,GAAG,EAAmB,CAAC;IACvC,KAAK,CAAS;IACd,QAAQ,CAAuB;IAC/B,SAAS,CAAU;IACnB,QAAQ,CAAU;IAElB,OAAO,CAAc;IACrB,SAAS,CAAmB;IAE5B,YACE,IAAY,EACZ,OAA6B,EAC7B,QAAiB,EACjB,OAAgB;QAEhB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,OAAO,GAAG,cAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC;YACpC,UAAU,EAAE,IAAI,CAAC,OAAO;YACxB,qBAAqB,EAAE,KAAK;SAC7B,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE3D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;YACnC,IAAA,iBAAS,EAAC,kCAAkC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CACd,OAA6B,EAC7B,QAA6B;QAE7B,aAAa,CACX,iBAAiB,IAAI,CAAC,SAAS,CAC7B,OAAO,CAAC,MAAM,CACf,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAC/C,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACjB,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3B,CAAC;QAED,2DAA2D;QAC3D,IAAI,OAAO,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACzD,MAAM,SAAS,GAAiB,EAAE,CAAC;gBACnC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC3B,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC5B,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBACrB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,2DAA2D;YAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC7C,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE;gBACtB,cAAc,EAAE,gCAAgC;gBAChD,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;YACH,MAAM,SAAS,GAAG,IAAA,gBAAM,GAAE,CAAC;YAC3B,MAAM,OAAO,GAAY;gBACvB,SAAS;gBACT,oEAAoE;gBACpE,4CAA4C;gBAC5C,sBAAsB,EAAE,SAAS;gBACjC,cAAc,EAAE;oBACd,aAAa,EAAE,IAAI,CAAC,iBAAiB,CACnC,QAAQ,CAAC,YAAY,EACrB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,CACf;oBACD,aAAa,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,YAAY,CAAC;oBAC5D,OAAO,EAAE,IAAI,CAAC,QAAQ;iBACvB;aACF,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEvC,MAAM,YAAY,GAAG,kBAAkB,IAAI,CAAC,KAAK,YAAY,SAAS,EAAE,CAAC;YACzE,aAAa,CACX,mCAAmC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CACnE,CAAC;YAEF,QAAQ,CAAC,KAAK,CACZ,IAAI,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE;oBACL,SAAS;oBACT,YAAY,EAAE;wBACZ,YAAY;qBACb;iBACF;aACF,CAAC,CACH,CAAC;YACF,OAAO,QAAQ,CAAC,GAAG,EAAE,CAAC;QACxB,CAAC;aAAM,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9C,aAAa,CACX,2BACE,OAAO,CAAC,MAAM,IAAI,gBACpB,gBACE,OAAO,CAAC,GACV,iBAAiB,MAAM,IAAI,CAAC,sBAAsB,CAChD,OAAO,CACR,iBAAiB,CACnB,CAAC;YAEF,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE;gBACtB,cAAc,EAAE,gCAAgC;gBAChD,eAAe,EAAE,UAAU;aAC5B,CAAC,CAAC;YACH,QAAQ,CAAC,KAAK,CACZ,IAAI,CAAC,SAAS,CAAC;gBACb,KAAK,EAAE,EAAE;aACV,CAAC,CACH,CAAC;YACF,OAAO,QAAQ,CAAC,GAAG,EAAE,CAAC;QACxB,CAAC;QAED,aAAa,CACX,WAAW,OAAO,CAAC,MAAM,gBAAgB,IAAI,CAAC,SAAS,CACrD,OAAO,CAAC,GAAG,CACZ,iBAAiB,MAAM,IAAI,CAAC,sBAAsB,CACjD,OAAO,CACR,iBAAiB,CACnB,CAAC;QACF,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,YAAY,CAAC,OAA0B;QACrC,qDAAqD;QACrD,IAAI,OAA4B,CAAC;QAEjC,MAAM,gBAAgB,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACnE,aAAa,CACX,kCAAkC,IAAI,CAAC,SAAS,CAC9C,OAAO,CAAC,WAAW,CAAC,IAAI,CACzB,gBAAgB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CACpD,CAAC;QAEF,IACE,gBAAgB,KAAK,EAAE;YACvB,gBAAgB,KAAK,SAAS;YAC9B,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,EACrC,CAAC;YACD,aAAa,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;YACvD,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAEpC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;QACrD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,sEAAsE;YACtE,wEAAwE;YACxE,wEAAwE;YACxE,yCAAyC;YACzC,wDAAwD;YACxD,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;YAC9C,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,+BAA+B,CACnE,OAAO,CACR;iBACE,IAAI,CACH,KAAK,IAAI,EAAE,CACT,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,cAAc,CAAC,CAChE;iBACA,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;gBACX,IAAA,iBAAS,EAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;gBAC7C,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,gCAAgC,CAAC,CAAC;gBACxD,MAAM,CAAC,CAAC;YACV,CAAC,CAAC,CAAC;QACP,CAAC;QAED,UAAU,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACzC,qCAAqC;YACrC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC5B,IAAI,CAAC,iBAAiB,CACpB,UAAU,EACV,EAAE,sDAEF,uBAAuB,OAAO,CAAC,IAAI,GAAG,CACvC,CAAC;gBACF,OAAO;YACT,CAAC;YAED,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC;YAE1C,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;gBACtB,IAAI,CAAC;oBACH,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBAC1C,CAAC;gBAAC,MAAM,CAAC;oBACP,SAAS,CAAC,gBAAgB,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;YAED,4DAA4D;YAC5D,IAAI,iBAA6D,CAAC;YAClE,IAAI,CAAC;gBACH,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAI,CAAC,iBAAiB,CACpB,UAAU,EACV,EAAE,sDAEF,2BAA2B,CAC5B,CAAC;gBACF,OAAO;YACT,CAAC;YAED,+BAA+B;YAC/B,IAAI,iBAAiB,CAAC,MAAM,KAAK,aAAa,EAAE,CAAC;gBAC/C,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;oBAC1B,IAAA,iBAAS,EAAC,mDAAmD,CAAC,CAAC;oBAE/D,IAAI,CAAC,iBAAiB,CACpB,UAAU,EACV,gBAAgB,2DAEhB,mDAAmD,CACpD,CAAC;oBACF,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC;oBACH,MAAM,cAAc,GAAG;wBACrB,aAAa,EAAE,IAAI,CAAC,iBAAiB,CACnC,iBAAiB,CAAC,MAAM,EAAE,YAAY,EACtC,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,CACf;wBACD,aAAa,EAAE,IAAI,CAAC,iBAAiB,CACnC,iBAAiB,CAAC,MAAM,EAAE,YAAY,CACvC;wBACD,OAAO,EAAE,IAAI,CAAC,QAAQ;qBACvB,CAAC;oBAEF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACvD,UAAU,EACV,cAAc,CACf,CAAC;oBAEF,MAAM,SAAS,GAAG,IAAA,gBAAM,GAAE,CAAC;oBAC3B,OAAO,GAAG;wBACR,SAAS;wBACT,sBAAsB,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC;wBACxD,cAAc;qBACf,CAAC;oBACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACzC,CAAC;gBAAC,OAAO,CAAM,EAAE,CAAC;oBAChB,IAAA,iBAAS,EAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;oBAE7C,IAAI,CAAC,iBAAiB,CACpB,UAAU,EACV,gBAAgB,2DAEhB,CAAC,EAAE,OAAO,IAAI,eAAe,CAC9B,CAAC;oBACF,OAAO;gBACT,CAAC;gBAED,kCAAkC;gBAClC,IAAI,CAAC,kBAAkB,CACrB;oBACE,EAAE,EAAE,iBAAiB,CAAC,EAAE;oBACxB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE;wBACN,SAAS,EAAE,OAAO,CAAC,SAAS;wBAC5B,YAAY,EAAE,EAAE;qBACjB;iBACF,EACD,UAAU,CACX,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,IAAA,iBAAS,EAAC,iCAAiC,CAAC,CAAC;gBAE7C,IAAI,CAAC,iBAAiB,CACpB,UAAU,EACV,gBAAgB,yDAEhB,iCAAiC,CAClC,CAAC;gBACF,OAAO;YACT,CAAC;YAED,IAAI,OAAO,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;gBACjD,IAAA,iBAAS,EAAC,mCAAmC,CAAC,CAAC;gBAE/C,IAAI,CAAC,iBAAiB,CACpB,UAAU,EACV,gBAAgB,yDAEhB,mCAAmC,CACpC,CAAC;gBACF,OAAO;YACT,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC;YAE7D,kCAAkC;YAClC,IAAI,iBAAiB,CAAC,MAAM,KAAK,eAAe,EAAE,CAAC;gBACjD,MAAM,eAAe,CAAC,KAAK,EAAE,CAAC;gBAC9B,IAAI,CAAC,kBAAkB,CACrB;oBACE,EAAE,EAAE,iBAAiB,CAAC,EAAE;oBACxB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,EAAE;iBACX,EACD,UAAU,CACX,CAAC;gBACF,OAAO;YACT,CAAC;YAED,6CAA6C;YAC7C,MAAM,eAAe,CAAC,WAAW,EAAE,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAEH,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;YAChC,aAAa,CAAC,QAAQ,UAAU,CAAC,aAAa,gBAAgB,CAAC,CAAC;YAEhE,4EAA4E;YAC5E,MAAM,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,+BAA+B,CAAC,OAAiB;QACrD,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAC1E,OAAO;QACT,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,sBAAsB,CAAC;QAC7D,OAAO,CAAC,sBAAsB,GAAG,SAAS,CAAC;QAC3C,KAAK,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED,iBAAiB,CAAC,YAAiB;QACjC,MAAM,mBAAmB,GACvB,YAAY,EAAE,WAAW,EAAE,mBAAmB,IAAI,KAAK,CAAC;QAC1D,MAAM,iBAAiB,GACrB,YAAY,EAAE,WAAW,EAAE,iBAAiB,IAAI,KAAK,CAAC;QACxD,OAAO,EAAC,mBAAmB,EAAE,iBAAiB,EAAC,CAAC;IAClD,CAAC;IAED,iBAAiB,CACf,YAAiB,EACjB,OAA6B,EAC7B,QAAiB;QAEjB,MAAM,kBAAkB,GACtB,YAAY,EAAE,WAAW,EAAE,CAAC,oBAAoB,CAAC,CAAC;QACpD,OAAO;YACL,UAAU,EAAE,kBAAkB,EAAE,IAAI,IAAI,EAAE;YAC1C,OAAO;YACP,QAAQ;YACR,YAAY,EAAE,kBAAkB,EAAE,MAAM,IAAI,SAAS;SACtD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,UAAgC,EAChC,cAA8B;QAE9B,IAAA,iBAAS,EAAC,8BAA8B,CAAC,CAAC;QAC1C,MAAM,eAAe,GAAG,MAAM,oCAAe,CAAC,GAAG,CAC/C,cAAc,CAAC,aAAa,EAC5B,cAAc,CAAC,aAAa,EAC5B,cAAc,CAAC,OAAO,CACvB,CAAC;QAEF,mEAAmE;QACnE,eAAe,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE;YACtD,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,IAAA,iBAAS,EAAC,sBAAsB,CAAC,CAAC;QAClC,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,wBAAwB,CACtB,OAAe,EACf,UAAgC;QAEhC,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,IAAI,CAAC;gBACH,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YACjC,CAAC;YAAC,MAAM,CAAC;gBACP,SAAS,CAAC,OAAO,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QACD,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED,kBAAkB,CAAC,MAAe,EAAE,UAAgC;QAClE,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IACzD,CAAC;IAED,iBAAiB,CACf,UAAgC,EAChC,gBAAyB,EACzB,SAAiB,EACjB,YAAoB;QAEpB,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAC1C,gBAAgB,EAChB,SAAS,EACT,YAAY,CACb,CAAC;QACF,KAAK,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,iBAAiB,CACf,gBAAqB,EACrB,SAAiB,EACjB,YAAoB;QAEpB,4DAA4D;QAC5D,2DAA2D;QAC3D,IAAI,SAAS,CAAC;QACd,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACjD,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;gBACxB,SAAS,GAAG,WAAW,CAAC,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;QAEV,OAAO;YACL,IAAI,EAAE,OAAO;YACb,EAAE,EAAE,SAAS;YACb,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE,YAAY;YACrB,kCAAkC;SACnC,CAAC;IACJ,CAAC;IAED,sBAAsB,CAAC,OAA6B;QAClD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC3B,IAAI,IAAI,KAAK,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACrB,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC5B,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAvcD,0CAucC"}