## Changelog

**5.0.1** — <small>_February 28, 2024_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/5.0.0...5.0.1)

**5.0.0** — <small>_September 2, 2023_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/4.0.3...5.0.0)

**4.0.3** — <small>_September 2, 2023_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/4.0.2...4.0.3)

**4.0.2** — <small>_February 3, 2021_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/4.0.1...4.0.2)

### Bug Fixes

- fix DeflateCRC32Stream to support Node.js 15.6.0+ (#31) (#32)

### Maintenance

- Bump actions/setup-node from v2.1.2 to v2.1.4 (#30)

**4.0.1** — <small>_November 18, 2020_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/4.0.0...4.0.1)

### Bug Fixes

- use crc-32 rather than crc module (#28)

### Maintenance

- Bump mocha from 8.2.0 to 8.2.1 (#25)
- Bump actions/checkout from v2.3.2 to v2.3.4 (#26)
- Bump actions/setup-node from v2.1.1 to v2.1.2 (#23)
- Bump mocha from 8.1.1 to 8.2.0 (#24)
- Bump mocha from 8.1.0 to 8.1.1 (#18)
- Bump actions/checkout from v2.3.1 to v2.3.2 (#19)
- Bump mocha from 8.0.1 to 8.1.0 (#17)
- Bump actions/setup-node from v2.1.0 to v2.1.1 (#16)

**4.0.0** — <small>_July 18, 2020_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/3.0.1...4.0.0)

* Bump actions/checkout from v1 to v2.3.1 (#13) @dependabot
* Bump readable-stream from 3.4.0 to 3.6.0 (#15) @dependabot
* Bump mocha from 6.2.0 to 8.0.1 (#14) @dependabot
* Bump actions/setup-node from v1 to v2.1.0 (#12) @dependabot
* remove support for node < 10 (#11) @ctalkington

**3.0.1** — <small>_August 2, 2019_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/3.0.0...3.0.1)

- update dependencies

**3.0.0** — <small>_April 29, 2019_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/2.0.0...3.0.0)

- Require Node.js 6.9, update dependencies, use modern JS syntax (GH #10)
- Do not use the deprecated Buffer() constructor (GH #8)
- remove node v0.10 and v0.12 support

**2.0.0** — <small>_February 13, 2017_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/1.0.1...2.0.0)

- adopt nodejs core Hash API (GH #4)

**1.0.1** — <small>_January 12, 2016_</small> — [Diff](https://github.com/archiverjs/node-crc32-stream/compare/1.0.0...1.0.1)

- Switch to node-crc for performance (GH #3)
- bump deps to ensure latest versions are used.

[Release Archive](https://github.com/archiverjs/node-crc32-stream/releases)
