"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputProcessor = void 0;
/*
 * Copyright 2023 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const protocol_js_1 = require("../../../protocol/protocol.js");
const assert_js_1 = require("../../../utils/assert.js");
const ActionDispatcher_js_1 = require("../input/ActionDispatcher.js");
const InputStateManager_js_1 = require("../input/InputStateManager.js");
class InputProcessor {
    #browsingContextStorage;
    #realmStorage;
    #inputStateManager = new InputStateManager_js_1.InputStateManager();
    constructor(browsingContextStorage, realmStorage) {
        this.#browsingContextStorage = browsingContextStorage;
        this.#realmStorage = realmStorage;
    }
    async performActions(params) {
        const context = this.#browsingContextStorage.getContext(params.context);
        const inputState = this.#inputStateManager.get(context.top);
        const actionsByTick = this.#getActionsByTick(params, inputState);
        const dispatcher = new ActionDispatcher_js_1.ActionDispatcher(inputState, context, await ActionDispatcher_js_1.ActionDispatcher.isMacOS(context).catch(() => false));
        await dispatcher.dispatchActions(actionsByTick);
        return {};
    }
    async releaseActions(params) {
        const context = this.#browsingContextStorage.getContext(params.context);
        const topContext = context.top;
        const inputState = this.#inputStateManager.get(topContext);
        const dispatcher = new ActionDispatcher_js_1.ActionDispatcher(inputState, context, await ActionDispatcher_js_1.ActionDispatcher.isMacOS(context).catch(() => false));
        await dispatcher.dispatchTickActions(inputState.cancelList.reverse());
        this.#inputStateManager.delete(topContext);
        return {};
    }
    async setFiles(params) {
        const realm = this.#realmStorage.findRealm({
            browsingContextId: params.context,
        });
        if (realm === undefined) {
            throw new protocol_js_1.NoSuchFrameException(`Could not find browsingContext ${params.context}`);
        }
        let isFileInput;
        try {
            const result = await realm.callFunction(String(function getFiles() {
                return (this instanceof HTMLInputElement &&
                    this.type === 'file' &&
                    !this.disabled);
            }), params.element, [], false, "none" /* Script.ResultOwnership.None */, {}, false);
            (0, assert_js_1.assert)(result.type === 'success');
            (0, assert_js_1.assert)(result.result.type === 'boolean');
            isFileInput = result.result.value;
        }
        catch {
            throw new protocol_js_1.NoSuchElementException(`Could not find element ${params.element.sharedId}`);
        }
        if (!isFileInput) {
            throw new protocol_js_1.UnableToSetFileInputException(`Element ${params.element.sharedId} is not a mutable file input.`);
        }
        // Our goal here is to iterate over the input element files and get their
        // file paths.
        const paths = [];
        for (let i = 0; i < params.files.length; ++i) {
            const result = await realm.callFunction(String(function getFiles(index) {
                if (!this.files) {
                    // We use `null` because `item` also returns null.
                    return null;
                }
                return this.files.item(index);
            }), params.element, [{ type: 'number', value: 0 }], false, "root" /* Script.ResultOwnership.Root */, {}, false);
            (0, assert_js_1.assert)(result.type === 'success');
            if (result.result.type !== 'object') {
                break;
            }
            const { handle } = result.result;
            (0, assert_js_1.assert)(handle !== undefined);
            const { path } = await realm.cdpClient.sendCommand('DOM.getFileInfo', {
                objectId: handle,
            });
            paths.push(path);
            // Cleanup the handle.
            void realm.disown(handle).catch(undefined);
        }
        paths.sort();
        // We create a new array so we preserve the order of the original files.
        const sortedFiles = [...params.files].sort();
        if (paths.length !== params.files.length ||
            sortedFiles.some((path, index) => {
                return paths[index] !== path;
            })) {
            const { objectId } = await realm.deserializeForCdp(params.element);
            // This cannot throw since this was just used in `callFunction` above.
            (0, assert_js_1.assert)(objectId !== undefined);
            await realm.cdpClient.sendCommand('DOM.setFileInputFiles', {
                files: params.files,
                objectId,
            });
        }
        else {
            // XXX: We should dispatch a trusted event.
            await realm.callFunction(String(function dispatchEvent() {
                this.dispatchEvent(new Event('cancel', {
                    bubbles: true,
                }));
            }), params.element, [], false, "none" /* Script.ResultOwnership.None */, {}, false);
        }
        return {};
    }
    #getActionsByTick(params, inputState) {
        const actionsByTick = [];
        for (const action of params.actions) {
            switch (action.type) {
                case "pointer" /* SourceType.Pointer */: {
                    action.parameters ??= { pointerType: "mouse" /* Input.PointerType.Mouse */ };
                    action.parameters.pointerType ??= "mouse" /* Input.PointerType.Mouse */;
                    const source = inputState.getOrCreate(action.id, "pointer" /* SourceType.Pointer */, action.parameters.pointerType);
                    if (source.subtype !== action.parameters.pointerType) {
                        throw new protocol_js_1.InvalidArgumentException(`Expected input source ${action.id} to be ${source.subtype}; got ${action.parameters.pointerType}.`);
                    }
                    break;
                }
                default:
                    inputState.getOrCreate(action.id, action.type);
            }
            const actions = action.actions.map((item) => ({
                id: action.id,
                action: item,
            }));
            for (let i = 0; i < actions.length; i++) {
                if (actionsByTick.length === i) {
                    actionsByTick.push([]);
                }
                actionsByTick[i].push(actions[i]);
            }
        }
        return actionsByTick;
    }
}
exports.InputProcessor = InputProcessor;
//# sourceMappingURL=InputProcessor.js.map