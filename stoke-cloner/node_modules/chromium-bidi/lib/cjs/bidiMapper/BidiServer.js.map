{"version": 3, "file": "BidiServer.js", "sourceRoot": "", "sources": ["../../../src/bidiMapper/BidiServer.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAKH,8DAAsD;AACtD,4CAAuD;AACvD,oEAA4D;AAK5D,+DAA+E;AAC/E,2FAAmF;AACnF,sEAA8D;AAC9D,uEAG2C;AAc3C,MAAa,UAAW,SAAQ,8BAA6B;IAC3D,aAAa,CAAmC;IAChD,UAAU,CAAgB;IAC1B,iBAAiB,CAAmB;IACpC,aAAa,CAAe;IAC5B,uBAAuB,GAAG,IAAI,kDAAsB,EAAE,CAAC;IACvD,OAAO,CAAY;IAEnB,sBAAsB,GAAG,CAAC,OAA6B,EAAE,EAAE;QACzD,KAAK,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YAClE,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,uBAAuB,GAAG,KAAK,EAAE,YAA6B,EAAE,EAAE;QAChE,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QAErC,IAAI,YAAY,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YAClC,OAAO,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC;QAC5C,CAAC;QAED,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF,YACE,aAA4B,EAC5B,aAA4B,EAC5B,gBAA2B,EAC3B,YAAoB,EACpB,oBAAyC,EACzC,OAAuB,EACvB,MAAmC,EACnC,MAAiB;QAEjB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,oCAAe,CACtC,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,UAAU,GAAG,aAAa,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,IAAI,8BAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpE,IAAI,CAAC,iBAAiB,GAAG,IAAI,sCAAgB,CAC3C,aAAa,EACb,gBAAgB,EAChB,IAAI,CAAC,aAAa,EAClB,YAAY,EACZ,oBAAoB,EACpB,IAAI,CAAC,uBAAuB,EAC5B,IAAI,8BAAY,EAAE,EAClB,OAAO,EAAE,mBAAmB,IAAI,KAAK,EACrC,OAAO,EAAE,iBAAiB,IAAI,KAAK,EACnC,MAAM,EACN,IAAI,CAAC,OAAO,CACb,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,EAAE,yCAA2B,CAAC,EAAC,OAAO,EAAE,KAAK,EAAC,EAAE,EAAE;YACnE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,EAAE,mDAEvB,CAAC,EAAC,OAAO,EAAE,KAAK,EAAC,EAAE,EAAE;YACnB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,aAA4B,EAC5B,aAA4B,EAC5B,gBAA2B,EAC3B,YAAoB,EACpB,OAAuB,EACvB,MAAmC,EACnC,MAAiB;QAEjB,0EAA0E;QAC1E,sEAAsE;QACtE,2EAA2E;QAC3E,6BAA6B;QAC7B,MAAM,CAAC,EAAC,iBAAiB,EAAC,EAAE,EAAC,WAAW,EAAC,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC7D,gBAAgB,CAAC,WAAW,CAAC,2BAA2B,CAAC;YACzD,gBAAgB,CAAC,WAAW,CAAC,mBAAmB,CAAC;SAClD,CAAC,CAAC;QACH,IAAI,oBAAoB,GAAG,SAAS,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,IACE,IAAI,CAAC,gBAAgB;gBACrB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAClD,CAAC;gBACD,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBAC7C,MAAM;YACR,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,UAAU,CAC3B,aAAa,EACb,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,oBAAoB,EACpB,OAAO,EACP,MAAM,EACN,MAAM,CACP,CAAC;QACF,0CAA0C;QAC1C,MAAM,gBAAgB,CAAC,WAAW,CAAC,2BAA2B,EAAE;YAC9D,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,iDAAiD;QACjD,MAAM,gBAAgB,CAAC,WAAW,CAAC,sBAAsB,EAAE;YACzD,UAAU,EAAE,IAAI;YAChB,sBAAsB,EAAE,IAAI;YAC5B,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,MAAM,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,mBAAmB,CACjB,YAA8C,EAC9C,KAAa;QAEb,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,uBAAuB;QAC3B,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,uBAAuB;aACzB,mBAAmB,EAAE;aACrB,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CACnC,CAAC;IACJ,CAAC;CACF;AAjJD,gCAiJC"}