{"name": "archiver-utils", "version": "4.0.1", "license": "MIT", "description": "utility functions for archiver", "homepage": "https://github.com/archiverjs/archiver-utils#readme", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/archiver-utils.git"}, "bugs": {"url": "https://github.com/archiverjs/archiver-utils/issues"}, "keywords": ["archiver", "utils"], "main": "index.js", "files": ["index.js", "file.js"], "engines": {"node": ">= 12.0.0"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"glob": "^8.0.0", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash": "^4.17.15", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "devDependencies": {"chai": "4.3.8", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "3.0.2"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}}