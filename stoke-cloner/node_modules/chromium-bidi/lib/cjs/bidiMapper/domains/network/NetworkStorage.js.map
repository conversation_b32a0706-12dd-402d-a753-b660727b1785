{"version": 3, "file": "NetworkStorage.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/network/NetworkStorage.ts"], "names": [], "mappings": ";;;AAkBA,+DAAgF;AAChF,gEAAwD;AACxD,oDAA8C;AAgB9C,yCAAyC;AACzC,MAAa,cAAc;IACzB;;;OAGG;IACM,WAAW,GAAG,IAAI,GAAG,EAAmC,CAAC;IAElE,kEAAkE;IACzD,aAAa,GAAG,IAAI,GAAG,EAA0C,CAAC;IAE3E,wEAAwE;IAC/D,kBAAkB,GAAG,IAAI,GAAG,EAAmC,CAAC;IAEzE,iBAAiB;QACf,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACH,YAAY,CAAC,KAA0B;QACrC,MAAM,WAAW,GAAsB,IAAA,gBAAM,GAAE,CAAC;QAChD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAE3C,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,SAA4B;QAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,sCAAwB,CAChC,cAAc,SAAS,mBAAmB,CAC3C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED,4DAA4D;IAC5D,aAAa;QACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,6EAA6E;IAC7E,oBAAoB;QAClB,MAAM,QAAQ,GAAoC,EAAE,CAAC;QAErD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACjC,MAAM,YAAY,GAAG,cAAc,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAEjE,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACnC,QAAQ,CAAC,IAAI,CAAC;wBACZ,UAAU,EAAE,GAAG;wBACf,YAAY;qBACb,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBACD,KAAK,MAAM,cAAc,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;oBAC/C,MAAM,UAAU,GACd,cAAc,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;oBAEvD,QAAQ,CAAC,IAAI,CAAC;wBACZ,UAAU;wBACV,YAAY;qBACb,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,QAAQ;YACR,mEAAmE;YACnE,8BAA8B;YAC9B,kBAAkB,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBAClE,OAAO,KAAK,CAAC,MAAM,CAAC,QAAQ,0DAAqC,CAAC;YACpE,CAAC,CAAC;SACH,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,EAAmB;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAED,UAAU,CAAC,OAAuB;QAChC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;IAED,aAAa,CAAC,EAAmB;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,4DAA4D;IAC5D,kBAAkB;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,oEAAoE;IACpE,kBAAkB;QAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED,iEAAiE;IACjE,MAAM,CAAC,qBAAqB,CAAC,UAA8B;QACzD,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;YACxB,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,OAAO,CAAC;YAC5B,KAAK,SAAS;gBACZ,OAAO,cAAc,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,EAC3B,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,QAAQ,EACR,MAAM,GACoB;QAC1B,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YAC5D,OAAO,GAAG,CAAC;QACb,CAAC;QAED,IAAI,GAAG,GAAW,EAAE,CAAC;QAErB,IAAI,QAAQ,EAAE,CAAC;YACb,GAAG,IAAI,QAAQ,CAAC;YAEhB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,GAAG,IAAI,GAAG,CAAC;YACb,CAAC;YAED,IAAI,cAAc,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7C,GAAG,IAAI,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,GAAG,IAAI,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,GAAG,IAAI,GAAG,CAAC;YACb,CAAC;YAED,GAAG,IAAI,QAAQ,CAAC;QAClB,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,GAAG,IAAI,GAAG,CAAC;YACb,CAAC;YAED,GAAG,IAAI,MAAM,CAAC;QAChB,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,qBAAqB,CAC1B,KAA6B;QAE7B,QAAQ,KAAK,EAAE,CAAC;YACd;gBACE,OAAO,SAAS,CAAC;YACnB,oEAA4C;YAC5C;gBACE,OAAO,UAAU,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,eAAe,CAAC,QAAgB;QACrC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,QAAQ,CAC3D,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAC3B,CAAC;IACJ,CAAC;IAED,iBAAiB,CAAC,SAA0B,EAAE,KAAqB;QACjE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,oBAAoB,CAAC,SAA0B;QAC7C,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,SAA0B;QAC1C,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;IAED,6EAA6E;IAC7E,oBAAoB,CAClB,SAA0B,EAC1B,KAA8B;QAE9B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,YAAY,GAAwB,EAAE,CAAC;QAE7C,KAAK,MAAM,CACT,WAAW,EACX,EAAC,MAAM,EAAE,WAAW,EAAC,EACtB,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAClC,IAAI,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC7B,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACjC,CAAC;qBAAM,IACL,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAC9B,cAAc,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CACxD,EACD,CAAC;oBACD,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,0DAA0D;IAC1D,MAAM,CAAC,eAAe,CACpB,UAA8B,EAC9B,GAAuB;QAEvB,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;YACxB,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,OAAO,KAAK,GAAG,CAAC;YACpC,KAAK,SAAS,CAAC,CAAC,CAAC;gBACf,OAAO,CACL,IAAI,0BAAU,CAAC;oBACb,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,MAAM,EAAE,UAAU,CAAC,MAAM;iBAC1B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CACtB,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF;AApRD,wCAoRC"}