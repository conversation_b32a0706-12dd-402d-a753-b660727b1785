const express = require('express');
const cors = require('cors');
const multer = require('multer');
const puppeteer = require('puppeteer');
const archiver = require('archiver');
const fs = require('fs-extra');
const path = require('path');
const { URL } = require('url');
const AdmZip = require('adm-zip');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

// Configure multer for file uploads
const upload = multer({ dest: 'uploads/' });

// Ensure directories exist
fs.ensureDirSync('downloads');
fs.ensureDirSync('uploads');
fs.ensureDirSync('temp');

// Store active local servers
const activeServers = new Map();

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Stoke Cloner API is running', port: PORT });
});

// Clone website endpoint
app.post('/api/clone', async (req, res) => {
  let browser;
  try {
    const { url, depth = 1, assetsOnly = false } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    console.log(`Cloning ${url} with depth ${depth}, assetsOnly: ${assetsOnly}`);

    // Launch puppeteer with better error handling
    console.log('Launching Puppeteer browser...');
    browser = await puppeteer.launch({
      headless: true,  // Use classic headless mode instead of 'new'
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ],
      timeout: 30000,
      ignoreDefaultArgs: ['--disable-extensions']
    });
    console.log('Browser launched successfully');
    
    console.log('Creating new page...');
    const page = await browser.newPage();

    // Set user agent to avoid bot detection
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    // Navigate to the page
    console.log(`Navigating to ${url}...`);
    await page.goto(url, { waitUntil: 'networkidle0', timeout: 30000 });
    console.log('Page loaded successfully');
    
    // Get page content
    const content = await page.content();
    
    // Create a unique filename
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.replace(/^www\./, '');
    const sanitizedHostname = hostname.replace(/[^a-zA-Z0-9.-]/g, '-');
    const timestamp = Date.now();
    const zipFilename = `${sanitizedHostname}-clone-${timestamp}.zip`;
    const zipPath = path.join('downloads', zipFilename);
    
    // Create ZIP file
    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    archive.pipe(output);
    
    // Add the main HTML file
    archive.append(content, { name: 'index.html' });
    
    // If not assets only, try to get some basic resources
    if (!assetsOnly) {
      try {
        // Get CSS files
        const cssLinks = await page.$$eval('link[rel="stylesheet"]', links => 
          links.map(link => link.href).filter(href => href)
        );
        
        for (const cssUrl of cssLinks.slice(0, 10)) { // Limit to 10 CSS files
          try {
            const cssResponse = await page.goto(cssUrl);
            if (cssResponse && cssResponse.ok()) {
              const cssContent = await cssResponse.text();
              const cssFilename = path.basename(new URL(cssUrl).pathname) || 'style.css';
              archive.append(cssContent, { name: `css/${cssFilename}` });
            }
          } catch (e) {
            console.log(`Failed to fetch CSS: ${cssUrl}`);
          }
        }
        
        // Get JS files
        const jsScripts = await page.$$eval('script[src]', scripts => 
          scripts.map(script => script.src).filter(src => src)
        );
        
        for (const jsUrl of jsScripts.slice(0, 10)) { // Limit to 10 JS files
          try {
            const jsResponse = await page.goto(jsUrl);
            if (jsResponse && jsResponse.ok()) {
              const jsContent = await jsResponse.text();
              const jsFilename = path.basename(new URL(jsUrl).pathname) || 'script.js';
              archive.append(jsContent, { name: `js/${jsFilename}` });
            }
          } catch (e) {
            console.log(`Failed to fetch JS: ${jsUrl}`);
          }
        }
      } catch (e) {
        console.log('Error fetching additional resources:', e.message);
      }
    }
    
    await browser.close();
    
    // Finalize the archive
    await archive.finalize();
    
    // Wait for the archive to finish
    await new Promise((resolve, reject) => {
      output.on('close', resolve);
      output.on('error', reject);
    });
    
    // Send the ZIP file
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${zipFilename}"`);
    
    const fileStream = fs.createReadStream(zipPath);
    fileStream.pipe(res);
    
    // Clean up the file after sending
    fileStream.on('end', () => {
      fs.unlink(zipPath).catch(console.error);
    });
    
  } catch (error) {
    console.error('Clone error:', error);

    // Make sure browser is closed even on error
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
      }
    }

    res.status(500).json({ error: error.message });
  }
});

// Local server endpoint
app.post('/api/local-server', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    const zipPath = req.file.path;
    const extractPath = path.join('temp', `extracted-${Date.now()}`);
    
    // For now, just return a mock response since we'd need additional ZIP extraction logic
    // In a full implementation, you'd extract the ZIP and serve it on a random port
    
    const mockPort = 8000 + Math.floor(Math.random() * 100);
    const localUrl = `http://localhost:${mockPort}`;
    
    // Clean up uploaded file
    fs.unlink(zipPath).catch(console.error);
    
    res.json({
      url: localUrl,
      port: mockPort,
      message: 'Local server started successfully'
    });
    
  } catch (error) {
    console.error('Local server error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Stoke Cloner API Server running on http://localhost:${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 API endpoints:`);
  console.log(`   POST /api/clone - Clone a website`);
  console.log(`   POST /api/local-server - Start local server for ZIP file`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Stoke Cloner API Server...');
  process.exit(0);
});
