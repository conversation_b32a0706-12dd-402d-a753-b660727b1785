"use strict";
/*
 * Copyright 2023 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleTransport = void 0;
const EventEmitter_1 = require("../utils/EventEmitter");
/**
 * Implements simple transport that allows sending string messages via
 * `sendCommand` and receiving them via `on('message')`.
 */
class SimpleTransport extends EventEmitter_1.EventEmitter {
    #sendCommandDelegate;
    /**
     * @param sendCommandDelegate delegate to be called in `sendCommand`.
     */
    constructor(sendCommandDelegate) {
        super();
        this.#sendCommandDelegate = sendCommandDelegate;
    }
    async sendCommand(plainCommand) {
        await this.#sendCommandDelegate(plainCommand);
    }
}
exports.SimpleTransport = SimpleTransport;
//# sourceMappingURL=SimpleTransport.js.map