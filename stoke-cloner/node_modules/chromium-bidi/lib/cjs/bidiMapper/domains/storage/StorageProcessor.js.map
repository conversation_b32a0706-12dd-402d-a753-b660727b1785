{"version": 3, "file": "StorageProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/storage/StorageProcessor.ts"], "names": [], "mappings": ";;;AAiBA,+DAAyE;AAEzE,wDAAgD;AAEhD,kDAA8C;AAE9C,wEAAgE;AAChE,gEAA4E;AAE5E;;GAEG;AACH,MAAa,gBAAgB;IAClB,iBAAiB,CAAY;IAC7B,uBAAuB,CAAyB;IAChD,OAAO,CAAuB;IAEvC,YACE,gBAA2B,EAC3B,sBAA8C,EAC9C,MAA4B;QAE5B,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,UAAU,CACd,MAAoC;QAEpC,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAExE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAC1D,oBAAoB,EACpB;YACE,gBAAgB,EAAE,YAAY,CAAC,WAAW;SAC3C,CACF,CAAC;QAEF,MAAM,mBAAmB,GAAG,WAAW,CAAC,OAAO;aAC5C,MAAM;QACL,yEAAyE;QACzE,8EAA8E;QAC9E,gBAAgB;QAChB,CAAC,CAAC,EAAE,EAAE,CACJ,YAAY,CAAC,YAAY,KAAK,SAAS;YACvC,CAAC,CAAC,YAAY,KAAK,YAAY,CAAC,YAAY,CAC/C;aACA,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,iCAAe,EAAC,CAAC,CAAC,CAAC;aAC9B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAEtD,OAAO;YACL,OAAO,EAAE,mBAAmB;YAC5B,YAAY;SACb,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAmC;QAEnC,MAAM,YAAY,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACxE,MAAM,SAAS,GAAG,IAAA,iCAAe,EAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAExD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,oBAAoB,EAAE;gBAC7D,OAAO,EAAE,CAAC,SAAS,CAAC;gBACpB,gBAAgB,EAAE,YAAY,CAAC,WAAW;aAC3C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YACtC,MAAM,IAAI,wCAA0B,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrD,CAAC;QACD,OAAO;YACL,YAAY;SACb,CAAC;IACJ,CAAC;IAED,4CAA4C,CAC1C,UAAsD;QAEtD,MAAM,iBAAiB,GAAW,UAAU,CAAC,OAAO,CAAC;QACrD,MAAM,eAAe,GACnB,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7D,sEAAsE;QACtE,+EAA+E;QAC/E,iFAAiF;QACjF,oCAAoC;QACpC,OAAO;YACL,WAAW,EACT,eAAe,CAAC,WAAW,KAAK,SAAS;gBACvC,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,eAAe,CAAC,WAAW;SAClC,CAAC;IACJ,CAAC;IAED,uCAAuC,CACrC,UAAiD;QAEjD,MAAM,wBAAwB,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC3D,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAC3C,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,GAAG,GAAG,sCAAgB,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC1B,mDAAmD;gBACnD,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,iFAAiF;gBACjF,oCAAoC;gBACpC,YAAY,GAAG,GAAG,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,EAAE,CAAC;YACpD,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GACf,UAAU,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC;QAE5E,yCAAyC;QACzC,uCAAuC;QACvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YACtD,IACE,GAAG,KAAK,SAAS;gBACjB,KAAK,KAAK,SAAS;gBACnB,CAAC,CAAC,MAAM,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EACtD,CAAC;gBACD,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,IAAI,wBAAwB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,OAAO,EAAE,CACZ,gBAAO,CAAC,SAAS,EACjB,+BAA+B,IAAI,CAAC,SAAS,CAC3C,MAAM,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAC7C,EAAE,CACJ,CAAC;QACJ,CAAC;QAED,OAAO;YACL,GAAG,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,YAAY,EAAC,CAAC;YACrD,GAAG,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,WAAW,EAAC,CAAC;SACpD,CAAC;IACJ,CAAC;IAED,2BAA2B,CACzB,aAAsD;QAEtD,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YAChC,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,IAAI,aAAa,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,4CAA4C,CAAC,aAAa,CAAC,CAAC;QAC1E,CAAC;QACD,IAAA,kBAAM,EAAC,aAAa,CAAC,IAAI,KAAK,YAAY,EAAE,wBAAwB,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,uCAAuC,CAAC,aAAa,CAAC,CAAC;IACrE,CAAC;IAED,YAAY,CAAC,MAAsB,EAAE,MAA6B;QAChE,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,CACL,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;YAChE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;YAC1D,8CAA8C;YAC9C,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS;gBACzB,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI;oBACtC,MAAM,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/C,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;YAC1D,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;YAC1D,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC;YACtE,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;YAChE,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC;YACtE,CAAC,MAAM,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CACjE,CAAC;IACJ,CAAC;CACF;AAlKD,4CAkKC"}