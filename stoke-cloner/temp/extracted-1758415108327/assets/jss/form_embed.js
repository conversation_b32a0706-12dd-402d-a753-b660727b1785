(()=>{function pe(){"use strict";function e(a,m,g){"addEventListener"in window?a.addEventListener(m,g,!1):"attachEvent"in window&&a.attachEvent("on"+m,g)}function i(){var a,m=["moz","webkit","o","ms"];for(a=0;a<m.length&&!E;a+=1)E=window[m[a]+"RequestAnimationFrame"];E||n(" RequestAnimationFrame not supported")}function t(){var a="Host page";return window.top!==window.self&&(a=window.parentIFrame?window.parentIFrame.getId():"Nested host page"),a}function o(a){return U+"["+t()+"]"+a}function n(a){l.log&&typeof window.console=="object"&&console.log(o(a))}function s(a){typeof window.console=="object"&&console.warn(o(a))}function f(a){function m(){function u(){c(y),p(),l.resized<PERSON>allback(y)}I("Height"),I("Width"),S(u,y,"resetPage")}function g(u){var x=u.id;n(" Removing iFrame: "+x),u.parentNode.removeChild(u),l.closedCallback(x),n(" --")}function v(){var u=D.substr(A).split(":");return{iframe:document.getElementById(u[0]),id:u[0],height:u[1],width:u[2],type:u[3]}}function I(u){var x=Number(l["max"+u]),N=Number(l["min"+u]),k=u.toLowerCase(),L=Number(y[k]);if(N>x)throw new Error("Value for min"+u+" can not be greater than max"+u);n(" Checking "+k+" is in range "+N+"-"+x),N>L&&(L=N,n(" Set "+k+" to min value")),L>x&&(L=x,n(" Set "+k+" to max value")),y[k]=""+L}function F(){var u=y.iframe.src;!u&&y.iframe.getAttribute("data-src")&&(u=y.iframe.getAttribute("data-src"));var x=a.origin,N=u.split("/").slice(0,3).join("/");if(l.checkOrigin&&(n(" Checking connection is from: "+N),""+x!="null"&&x!==N))throw new Error("Unexpected message received from: "+x+" for "+y.iframe.id+". Message was: "+a.data+". This error can be disabled by adding the checkOrigin: false option.");return!0}function $(){return U===(""+D).substr(0,A)}function G(){var u=y.type in{true:1,false:1};return u&&n(" Ignoring init message from meta parent page"),u}function z(u){return D.substr(D.indexOf(":")+ie+u)}function _(u){n(" MessageCallback passed: {iframe: "+y.iframe.id+", message: "+u+"}"),l.messageCallback({iframe:y.iframe,message:JSON.parse(u)}),n(" --")}function ge(){if(y.iframe===null)throw new Error("iFrame ("+y.id+") does not exist on "+B);return!0}function le(u){var x=u.getBoundingClientRect();return r(),{x:parseInt(x.left,10)+parseInt(h.x,10),y:parseInt(x.top,10)+parseInt(h.y,10)}}function ce(u){function x(){h=L,me(),n(" --")}function N(){return{x:Number(y.width)+k.x,y:Number(y.height)+k.y}}var k=u?le(y.iframe):{x:0,y:0},L=N();n(" Reposition requested from iFrame (offset x:"+k.x+" y:"+k.y+")"),window.top!==window.self?window.parentIFrame?u?parentIFrame.scrollToOffset(L.x,L.y):parentIFrame.scrollTo(y.width,y.height):s(" Unable to scroll to requested position, window.parentIFrame not found"):x()}function me(){l.scrollCallback(h)!==!1&&p()}function he(u){function x(we){var K=le(we);n(" Moving to in page link (#"+N+") at x: "+K.x+" y: "+K.y),h={x:K.x,y:K.y},me(),n(" --")}var N=u.split("#")[1]||"",k=decodeURIComponent(N),L=document.getElementById(k)||document.getElementsByName(k)[0];window.top!==window.self?window.parentIFrame?parentIFrame.moveToAnchor(N):n(" In page link #"+N+" not found and window.parentIFrame not found"):L?x(L):n(" In page link #"+N+" not found")}function ye(){switch(y.type){case"close":g(y.iframe),l.resizedCallback(y);break;case"message":_(z(6));break;case"scrollTo":ce(!1);break;case"scrollToOffset":ce(!0);break;case"inPageLink":he(z(9));break;case"reset":d(y);break;case"init":m(),l.initCallback(y.iframe);break;default:m()}}var D=a.data,y={};$()&&(n(" Received: "+D),y=v(),!G()&&ge()&&F()&&(ye(),H=!1))}function r(){h===null&&(h={x:window.pageXOffset!==void 0?window.pageXOffset:document.documentElement.scrollLeft,y:window.pageYOffset!==void 0?window.pageYOffset:document.documentElement.scrollTop},n(" Get page position: "+h.x+","+h.y))}function p(){h!==null&&(window.scrollTo(h.x,h.y),n(" Set page position: "+h.x+","+h.y),h=null)}function d(a){function m(){c(a),C("reset","reset",a.iframe)}n(" Size reset requested by "+(a.type==="init"?"host page":"iFrame")),r(),S(m,a,"init")}function c(a){function m(g){a.iframe.style[g]=a[g]+"px",n(" IFrame ("+a.iframe.id+") "+g+" set to "+a[g]+"px")}l.sizeHeight&&m("height"),l.sizeWidth&&m("width")}function S(a,m,g){g!==m.type&&E?(n(" Requesting animation frame"),E(a)):a()}function C(a,m,g){n("["+a+"] Sending msg to iframe ("+m+")"),g.contentWindow.postMessage(U+m,"*")}function R(){function a(){function z(_){1/0!==l[_]&&l[_]!==0&&($.style[_]=l[_]+"px",n(" Set "+_+" = "+l[_]+"px"))}z("maxHeight"),z("minHeight"),z("maxWidth"),z("minWidth")}function m(z){return z===""&&($.id=z="iFrameResizer"+ee++,n(" Added missing iframe ID: "+z+" ("+$.src+")")),z}function g(){n(" IFrame scrolling "+(l.scrolling?"enabled":"disabled")+" for "+G),$.style.overflow=l.scrolling===!1?"hidden":"auto",$.scrolling=l.scrolling===!1?"no":"yes"}function v(){(typeof l.bodyMargin=="number"||l.bodyMargin==="0")&&(l.bodyMarginV1=l.bodyMargin,l.bodyMargin=""+l.bodyMargin+"px")}function I(){return G+":"+l.bodyMarginV1+":"+l.sizeWidth+":"+l.log+":"+l.interval+":"+l.enablePublicMethods+":"+l.autoResize+":"+l.bodyMargin+":"+l.heightCalculationMethod+":"+l.bodyBackground+":"+l.bodyPadding+":"+l.tolerance}function F(z){e($,"load",function(){var _=H;C("iFrame.onload",z,$),!_&&l.heightCalculationMethod in Y&&d({iframe:$,height:0,width:0,type:"init"})}),C("init",z,$)}var $=this,G=m($.id);g(),a(),v(),F(I())}function Z(a){if(typeof a!="object")throw new TypeError("Options is not an object.")}function W(a){a=a||{},Z(a);for(var m in w)w.hasOwnProperty(m)&&(l[m]=a.hasOwnProperty(m)?a[m]:w[m])}function j(){function a(m){if(!m.tagName)throw new TypeError("Object is not a valid DOM element");if(m.tagName.toUpperCase()!=="IFRAME")throw new TypeError("Expected <IFRAME> tag, found <"+m.tagName+">.");R.call(m)}return function(m,g){switch(W(m),typeof g){case"undefined":case"string":Array.prototype.forEach.call(document.querySelectorAll(g||"iframe"),v=>{let I=v?.src||"";(I.includes("/form")||I.includes("/survey")||I.includes("/quiz")||I.includes("/booking")||I.includes("/group")||I.includes("/service-menu"))&&a(v)});break;case"object":a(g);break;default:throw new TypeError("Unexpected data type ("+typeof g+").")}}}function de(a){a.fn.iFrameResize=function(m){return W(m),this.filter("iframe").each(R).end()}}var ee=0,H=!0,te="message",ie=te.length,U="[iFrameSizer]",A=U.length,B="",h=null,E=window.requestAnimationFrame,Y={max:1,scroll:1,bodyScroll:1,documentElementScroll:1},l={},w={autoResize:!0,bodyBackground:null,bodyMargin:null,bodyMarginV1:8,bodyPadding:null,checkOrigin:!0,enablePublicMethods:!1,heightCalculationMethod:"offset",interval:32,log:!1,maxHeight:1/0,maxWidth:1/0,minHeight:0,minWidth:0,scrolling:!1,sizeHeight:!0,sizeWidth:!1,tolerance:0,closedCallback:function(){},initCallback:function(){},messageCallback:function(){},resizedCallback:function(){},scrollCallback:function(){return!0}};i(),e(window,"message",f),window.jQuery&&de(jQuery),typeof define=="function"&&define.amd?define([],j):typeof exports=="object"?module.exports=j():window.iFrameResize=j()}pe();function ne(){"use strict";var e={createWidget:function(){iFrameResize({log:!1,checkOrigin:!1,enablePublicMethods:!0,scrolling:!0,resizedCallback:function(i){for(var t=document.getElementsByClassName("containerModal"),o=0;o<t.length;o++)t[o].getElementsByTagName("iframe").length>0&&(t[o].style.position="absolute");if(i?.iframe){let s=(parseInt(i.iframe.style.height)||0)+5;i.iframe.style.height=s+"px"}}})}};window.onload=function(){e.createWidget()},e.createWidget()}ne();var be=e=>{let{shadow:i}=e;return`${i?.horizontal}px ${i?.vertical}px ${i?.blur}px ${i?.spread}px #${i?.color}`};function J(e){try{return decodeURIComponent(e)}catch{}}function xe(e){for(var i=e.split("&"),t={},o=0;o<i.length;o++){var n=i[o].split("="),s=J(n[0]),f=J(n[1]);if(typeof t[s]>"u")t[s]=J(f);else if(typeof t[s]=="string"){var r=[t[s],J(f)];t[s]=r}else t[s].push(J(f))}return t}var X=document.getElementsByTagName("iframe"),oe=[];for(let e=0;e<X.length;e++)X[e].id&&!oe.includes(X[e].id)&&oe.push(X[e].id);function ve(e){try{let i=window[e],t="__storage_test__";return i.setItem(t,t),i.removeItem(t),!0}catch{return!1}}function Ie(e){if(!ve("localStorage"))return;let i=localStorage.getItem(e);if(!i)return null;let t=JSON.parse(i);return new Date().getTime()>t.expiry?(localStorage.removeItem(e),null):t.value}if(!document.getElementById("embeddedIframes")){let e=i=>{let t=document.createElement("style");return t.type="text/css",t.innerText=i,t.id="embeddedIframes",document.head.appendChild(t),t};fe=".ep-header,.ep-iFrameContainer,.ep-overlay{display:none}.ep-iFrame{border:none}.ep-iFrameLarge{height:500px;overflow:auto}.ep-overflow{overflow:unset}.ep-overlay{z-index:10000;position:fixed;top:0;bottom:0;left:0;right:0;background:rgba(0,0,0,.5);transition:opacity .2s;width:100%;justify-content:center;align-items:center}.ep-wrapper{width:100%}.ep-header{justify-content:flex-end;position:fixed;border-radius:5px;z-index:1}.ep-close-icon,.ep-minimize-icon{background:#e8e8e8;width:23px;height:23px;margin:3px 6px 0 0;z-index:999;color:#4a4a4a;transition:background .25s;text-align:center;cursor:pointer}.ep-minimize-icon{border-radius:50%;line-height:.3ch;font-size:22px;border:1px solid #ccc;font-family:sans-serif}.ep-close-icon{border-radius:50%;line-height:1.5ch;font-size:20px;border:1px solid #ccc;font-family:Montserrat}.ep-minimize-text-container{height:100%;text-align:center}.ep-minimize-tc-sticky{writing-mode:vertical-lr;justify-content:center}.ep-minimize-tc-sticky-left{transform:rotate(360deg)}.ep-minimize-tc-sticky-right{transform:rotate(180deg)}.ep-minimize-tc-polite{align-items:end;width:100%}.ep-minimize-tc-polite-left{justify-content:start}.ep-minimize-tc-polite-right{justify-content:end}.ep-minimize-text{background-color:#000;cursor:pointer;color:#fff;white-space:nowrap;overflow:hidden;max-width:380px;text-overflow:ellipsis}.ep-minimize-text-polite{margin-bottom:8px}.ep-minimize-text-polite-left{margin-left:25px;margin-right:8px}.ep-minimize-text-polite-right{margin-left:8px;margin-right:25px}.ep-sticky-sidebar{top:50%;z-index:9999;position:fixed}.ep-height,.ep-sticky-sidebar .ep-wrapper{height:100%}.ep-polite-slide-in{z-index:9999;position:fixed}.ep-inline,.ep-popup{position:relative;margin:auto}.ep-polite-slide-in .ep-wrapper{position:absolute}.ep-popup{z-index:9999}.ep-inline{overflow:unset;boxshadow:none}.ep-right{right:15px}.ep-left{left:15px}.ep-bottom{bottom:15px}@media only screen and (max-width:550px){.ep-iFrame,.ep-iFrameContainer{width:100%}.ep-popup{width:100%!important}.ep-right{right:0}.ep-left{left:0}.ep-bottom{bottom:0}}",e(fe)}var fe,V={},M={},Q=!1,ae={},P="STICKY_SIDEBAR",q="POLITE_SLIDE_IN",T="POPUP",O="INLINE",b={};function ze({id:e,borderRadius:i,boxShadow:t,height:o,layout:n}){let s=document.createElement("div");return s.setAttribute("id",`${e}-div`),s.className="ep-iFrameContainer",s.style.cssText=`border-radius: ${i}px; ${n.id!=O?`box-shadow: ${t}`:""};`,s}function se(e,i){let t=e.parentNode;t?t.insertBefore(i,e.nextSibling):document.body.appendChild(i)}function Ne({id:e,iFrame:i,iFrameContainer:t}){let o=document.getElementById(`${e}-overlay`);return o=document.createElement("div"),o.setAttribute("id",`${e}-overlay`),o.className="ep-overlay",se(i,o),o.appendChild(t),o}function ke({id:e}){let i=document.createElement("div");return i.setAttribute("id",`${e}-header`),i.className="ep-header",i}function Se({id:e,layout:i,width:t,triggerType:o,triggerValue:n,height:s,background:f}){let r,p=document.getElementById(`${e}-header`);if(ae[e]){p&&(p.style.display=i.id===O?"none":"flex",p.style.width=window.matchMedia(`(max-width: ${t}px)`).matches?"100%":`${t}px`);let d=document.getElementById(`${e}-div`),c=document.getElementById(`${e}-overlay`),S=document.getElementById(`${e}`);i.id!==O&&(d.style.background=`#${f}`,Number(s)>=500?d.classList.add("ep-iFrameLarge"):(i.id!==T&&(d.style.height=`${Number(s)}px`),d.classList.add("ep-overflow"))),o==="showAfter"&&Number(n)&&(!Object.keys(M).length||!M[e])&&(r=setTimeout(()=>{clearTimeout(r),i.id===T&&c&&(c.style.display="flex"),d.style.display="block",S.style.display="block",i.id!==O&&(Number(s)>=500?d.classList.add("ep-iFrameLarge"):(d.style.height=`${Number(s)}px`,d.classList.add("ep-overflow")))},Number(n*1e3)))}}function Ee({id:e,layout:i}){let t=document.getElementById(`${e}-minimize-icon`);return i.id!==T&&(t=document.createElement("div"),t.setAttribute("id",`${e}-minimize-icon`),t.innerText="_",t.className="ep-minimize-icon"),t}function $e({id:e,layout:i}){let t=document.getElementById(`${e}-close-icon`);return(i.id===T||i.id!==T&&!i.allowMinimize)&&(t=document.createElement("div"),t.setAttribute("id",`${e}-close-icon`),t.innerText="x",t.className="ep-close-icon"),t}function Le({id:e,layout:i}){let t=document.createElement("div");return t.setAttribute("id",`${e}-minimize-text`),t.className="ep-minimize-text-container",t.style.display="none",i.id===P?(t.classList.add("ep-minimize-tc-sticky"),i.isLeftAligned?t.classList.add("ep-minimize-tc-sticky-left"):t.classList.add("ep-minimize-tc-sticky-right")):i.id===q&&(t.classList.add("ep-minimize-tc-polite"),i.isLeftAligned?t.classList.add("ep-minimize-tc-polite-left"):t.classList.add("ep-minimize-tc-polite-right")),t}function Me({id:e,formName:i,layout:t}){let o=document.createElement("span");return o.setAttribute("id",`${e}-minimize-text-span-`),o.innerText=t.minimizedTitle||i,o.className="ep-minimize-text",o.title=t.minimizedTitle||i,o.style.padding=t.id===P?"20px 10px":"10px 30px",t.id===q&&(o.classList.add("ep-minimize-text-polite"),t.isLeftAligned?o.classList.add("ep-minimize-text-polite-left"):o.classList.add("ep-minimize-text-polite-right")),o}function Te({iFrameContainer:e,wrapperDiv:i,height:t,layout:o,width:n}){e.classList.add("ep-sticky-sidebar"),o.isRightAligned?e.classList.add("ep-right"):e.classList.add("ep-left"),Number(t)>=500?e.style.marginTop="-250px":e.style.marginTop=`-${t/2}px`}function Ae({iFrameContainer:e,wrapperDiv:i,width:t,layout:o}){o.isRightAligned?e.classList.add("ep-right","ep-polite-slide-in","ep-bottom"):e.classList.add("ep-left","ep-polite-slide-in","ep-bottom")}function _e({iFrameContainer:e,iFrame:i,width:t}){e.style.width=window.matchMedia(`(max-width: ${t}px)`).matches?"100%":`${t}px`,e.classList.add("ep-popup")}function Oe({activationType:e,getIframeDetails:i,activationValue:t,deactivationType:o,deactivationValue:n,id:s}){e==="activateOnVisit"&&Number(t)?(Q=i.visit>=Number(t),o==="leadCollected"&&i.leadCollected||Q&&o==="deactivateAfter"&&i.visit-Number(t)>=Number(n)?M[s]=!0:Q||(M[s]=!0)):o==="leadCollected"&&i.leadCollected?M[s]=!0:o==="deactivateAfter"&&(M[s]=i.visit>Number(n))}function Ce({triggerValue:e,activationType:i,iframeActivated:t,layout:o,overlay:n,header:s,iFrameContainer:f,iFrame:r,height:p,id:d}){window.addEventListener("scroll",()=>{(o.id===O||document.getElementById(`${d}-minimize-text`)?.style.display==="none")&&!M[d]&&Number(e)&&(i==="alwaysActivated"||t)&&(document.body.offsetHeight-window.innerHeight)*(Number(e)/100)<=Math.round(window.scrollY)&&(o.id===T&&(n.style.display="flex"),f.style.display="block",o.id!==O&&(s.style.display="flex",Number(p)>=500?f.classList.add("ep-iFrameLarge"):(f.style.height=`${Number(p)}px`,f.classList.add("ep-overflow"))),r.style.display="block")})}function Be({iFrameContainer:e,iFrame:i,header:t,width:o,border:n,layout:s,id:f,height:r}){window.matchMedia(`(max-width: ${o}px)`).addListener(d=>{d.matches?(document.getElementById(`${f}-minimize-text`)?.style.display==="none"?(e.style.width="100%",i.style.width="100%"):s.id===q&&(e.style.width="50%"),t.style.width="100%"):(document.getElementById(`${f}-minimize-text`)?.style.display==="none"?e.style.width=`${o}px`:(e.style.marginTop=Number(r)>=500?"-250px":`-${r/2}px`,s.id===P&&(e.style.width="50px")),t.style.width=`${o}px`)})}function Fe({closeIconEle:e,iFrameContainer:i,overlay:t,layout:o,id:n}){e.addEventListener("click",()=>{i.style.display="none",o.id===T&&(t.style.display="none"),M={...M,[n]:!0}})}function Re({minimizeIconEle:e,iFrame:i,header:t,height:o,wrapperDiv:n,iFrameContainer:s,minimizeTextEle:f,layout:r,width:p}){e.addEventListener("click",()=>{i.style.display="none",t.style.display="none",s.style.boxShadow="none",s.style.background="transparent",r.id===q&&(s.style.height="50px",s.style.width="50%"),r.id===P&&(Number(o)<=500&&(n.style.height="100%"),s.style.width="50px"),f.style.display="flex",f.firstChild.style.borderRadius=r.id===P?"0px 8px 8px 0px":"8px 8px 0px 0px",s.style.border="none"})}function Pe({minimizeTextEle:e,iFrame:i,iFrameContainer:t,header:o,styles:n,height:s,width:f,formId:r,shadow:p}){e.firstChild.addEventListener("click",()=>{i.style.display="block",t.style.boxShadow=p,e.style.display="none",window.matchMedia(`(max-width: ${f}px)`).matches?(t.style.width="100%",i.style.width="100%"):t.style.width=`${f}px`,window.matchMedia(`(max-width: ${f}px)`).matches&&(t.style.marginTop=`-${s/2}px`),t.style.height=Number(s)>=500?"500px":`${Number(s)}px`,t.style.background=`#${n[r]}`,o.style.display="flex"})}function ue(e){return e?.src.includes("/form")||e?.src.includes("/survey")||e?.src.includes("/quiz")||e?.src.includes("/booking")||e?.src.includes("/group")}function qe(e){return e?.src.includes("/survey")||e?.src.includes("/quiz")||e?.src.includes("/booking")||e?.src.includes("/group")}function We(e,i,t){(i?.src.includes("/form")||i?.src.includes("/survey")||i?.src.includes("/quiz"))&&e>800&&window.innerHeight>800&&(t.style.height="800px")}function re(e){if(!!e)return e.includes("'")&&!e.includes('"')?e=JSON.parse(e.replace(/'/g,'"')):e=JSON.parse(e),e}window.onmessage=function(e){if(e.data[0]==="iframeLoaded"&&ne(),e.data[0]=="fetch-query-params"){let n;e.data[4]&&(n=JSON.parse(e.data[4]),b={...b,[e.data[3]]:{background:n.background,width:n.width,height:n.ac_branding?n.height+(n.headerImageSrc?430:155):n.height+(n.headerImageSrc?310:35),border:n.border,boxShadow:be(n)}});var i=location.search.substring(1),t=Ie(`v3_first_session_event_${e.data[2]}`);V=xe(i);var o=new URL(document.location.href);t&&t.url_params&&Object.keys(t.url_params).forEach(function(f){f&&t.url_params[f]&&o.searchParams.append(f,t.url_params[f])});let s=document.querySelectorAll("iframe[id]");for(let f of s){let r=f,p=r.id,d=r?.dataset?.formId,c=r?.dataset?.layout;if(!ue(r))continue;let S=r?.dataset?.triggerType,C=r?.dataset?.triggerValue,R=r?.dataset?.activationType,Z=r?.dataset?.activationValue,W=r?.dataset?.deactivationType,j=r?.dataset?.deactivationValue,de=r?.dataset?.isSurvey,ee=b[d]?.border.border,H=b[d]?.border?.radius,te=b[d]?.boxShadow,ie=r?.dataset?.formName;if(!c){qe(f)&&r.contentWindow.postMessage(["query-params",V,o.toString(),t?.referrer?t?.referrer:document.referrer,p],"*");continue}if(c&&(c=re(c)),r.parentNode?.id===`${p}-wrapper`)return r.contentWindow.postMessage(["query-params",V,o.toString(),t?.referrer?t?.referrer:document.referrer,`${r?.dataset?.layoutIframeId}`],"*");let A=localStorage.getItem(`embedded_iframe_${p}`);A?(A=JSON.parse(A),A.visit=A.visit+1,localStorage.setItem(`embedded_iframe_${p}`,JSON.stringify(A))):(localStorage.setItem(`embedded_iframe_${p}`,JSON.stringify({id:p,visit:1,layout:c})),A={id:p,visit:1});let B,h,E,Y,l=b[d]?.height||r?.dataset?.height,w=ze({id:p,borderRadius:H,boxShadow:te,height:l,layout:c}),a=$e({id:p,layout:c}),m=Ee({id:p,layout:c}),g=document.createElement("div");g.setAttribute("id",`${p}-wrapper`),g.className="ep-wrapper",g.style.cssText=`border-radius: ${H}px`,c.id!==O?(r.style.border="none",c.id===T?(r.style.position=null,We(Number(l),r,w)):(r.style.height=`${l}px`,r.style.position=c.id===q?"relative":"absolute"),c.id===T?B=Ne({id:p,iFrame:r,iFrameContainer:w}):se(r,w),g?.appendChild(r),w.appendChild(g),c.id===P&&Te({iFrameContainer:w,wrapperDiv:g,height:l,width:b[d]?.width,layout:c}),c.id===q&&Ae({iFrameContainer:w,wrapperDiv:g,width:b[d]?.width,layout:c}),c.id===T&&_e({iFrameContainer:w,iFrame:r,width:b[d]?.width}),h=ke({id:p}),g.insertBefore(h,r),w.style.width=window.matchMedia(`(max-width: ${b[d]?.width}px)`).matches?"100%":`${b[d]?.width}px`,r.style.width=window.matchMedia(`(max-width: ${b[d]?.width}px)`).matches?"100%":`${b[d]?.width}px`,h.style.width=window.matchMedia(`(max-width: ${b[d]?.width}px)`).matches?"100%":`${b[d]?.width}px`,Be({iFrameContainer:w,iFrame:r,header:h,width:b[d]?.width,border:ee,layout:c,id:p,height:l}),m&&(c.allowMinimize||(m.style.display="none"),h.appendChild(m)),a&&h.appendChild(a),E=Le({id:p,layout:c}),Y=Me({id:p,formName:ie,layout:c}),E.appendChild(Y),g.insertBefore(E,r)):(se(r,w),g?.appendChild(r),w.appendChild(g)),ue(f)&&r.contentWindow.postMessage(["query-params",V,o.toString(),t?.referrer?t?.referrer:document.referrer,p],"*"),Oe({activationType:R,getIframeDetails:A,activationValue:Z,deactivationType:W,deactivationValue:j,id:p}),M[p]&&W!=="neverDeactivate"?(r.style.display="none",w.style.display="none",B&&(B.style.display="none")):S==="showOnScrolling"&&Number(C)?Ce({triggerValue:C,activationType:R,iframeActivated:Q,layout:c,overlay:B,header:h,iFrameContainer:w,iFrame:r,height:l,id:p}):!Number(C)&&(R==="alwaysActivated"||Q)&&(c.id!==O&&(Number(l)>=500?w.classList.add("ep-iFrameLarge"):(c.id!==T&&(w.style.height=`${Number(l)}px`),w.classList.add("ep-overflow"))),c.id===T&&(B.style.display="flex"),w.style.display="block",r.style.display="block"),c.id!==O&&(a&&Fe({closeIconEle:a,iFrameContainer:w,overlay:B,layout:c,id:p}),m&&Re({minimizeIconEle:m,iFrame:r,header:h,height:l,wrapperDiv:g,iFrameContainer:w,minimizeTextEle:E,layout:c,width:b[d]?.width}),E&&Pe({minimizeTextEle:E,iFrame:r,iFrameContainer:w,header:h,styles:b,height:l,width:b[d]?.width,formId:d,shadow:b[d]?.boxShadow})),r.contentWindow.postMessage(["query-params",V,o.toString(),t?.referrer?t?.referrer:document.referrer,`${r?.dataset?.layoutIframeId}`],"*"),s.forEach(v=>{if(v.src.includes("/form")){let I=v.dataset.formId;v.onload=function(){let F;F=re(v?.dataset?.layout),F&&(ae={...ae,[v.dataset.layoutIframeId]:!0},Se({id:v.dataset.layoutIframeId,layout:F,width:b[I]?.width,height:b[I]?.height||v.dataset.height,triggerValue:v.dataset.triggerValue,triggerType:v.dataset.triggerType,background:b[I]?.background}))}}})}}else if(e.data[0]=="fetch-sticky-contacts"){let n=d=>{let c;if(typeof localStorage<"u")try{c=localStorage.getItem(d)}catch{}return c},s=d=>{try{let c;return typeof localStorage!==void 0&&(c=localStorage.getItem("_ud")),c}catch{return null}},f=d=>{let c=d;return d&&typeof c=="string"&&(c=JSON.parse(c)),c},r=d=>{let c=s(d),S=f(c);if(S&&"location_id"in S){let{location_id:C}=S;return C===d?S:null}return null};document.querySelectorAll("iframe[id]").forEach(d=>{d?.contentWindow.postMessage(["sticky-contacts",r(e.data[1]),n(e.data[1])],"*")})}else if(e.data[0]=="set-sticky-contacts"){if(typeof localStorage<"u")try{if(e.data[1]&&e.data[2])if(e.data[1]===`embedded_iframe_${e.data[2]}`){let n=localStorage.getItem(`embedded_iframe_${e.data[2]}`),s=5e3,f,r=re(document.getElementById(oe[0]).dataset.layout);if(n){n=JSON.parse(n),localStorage.setItem(`embedded_iframe_${e.data[2]}`,JSON.stringify({...n,leadCollected:!0}));let p=document.getElementById(`${e.data[2]}-overlay`),d=document.getElementById(`${e.data[2]}-div`);r.id!==O&&(f=setTimeout(()=>{clearTimeout(f),p&&(p.style.display="none"),d&&(d.style.display="none",M[e.data[2]]=!0)},s))}}else localStorage.setItem(e.data[1],e.data[2]);e.data[3]&&e.data[4]&&localStorage.setItem(e.data[3],e.data[4])}catch(n){console.error(n)}}else e.data[0]=="modify-parent-url"&&window.history.replaceState(null,"",e.data[1])};})();
